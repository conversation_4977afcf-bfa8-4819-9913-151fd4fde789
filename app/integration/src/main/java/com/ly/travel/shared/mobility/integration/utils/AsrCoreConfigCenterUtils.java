package com.ly.travel.shared.mobility.integration.utils;

import com.alibaba.fastjson.JSON;
import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.shared.mobility.integration.utils.dto.DriverServiceTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;


@Slf4j
public class AsrCoreConfigCenterUtils {

    private static String getValue(String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    private static String getValue(String appUk, String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(appUk, key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    public static int getSfcOrderBeforeDays() {
        int days = 90;
        try {
            String str = getValue("sfc_order_before_days");

            if (StringUtils.isNotBlank(str)) {
                days = Integer.parseInt(str);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return days;
    }

    public static DriverServiceTagDTO getDriverServiceTagList() {
        String jsonStr = null;
        try {
            jsonStr = getValue("groundtravel.shared.mobility.asr.admin", "driver_service_tag_config");

            if (StringUtils.isBlank(jsonStr)) {
                return DriverServiceTagDTO.getDefault();
            }
            return JacksonUtils.fromJSONString(jsonStr, DriverServiceTagDTO.class);
        } catch (Exception e) {
            log.error("获取统一配置的司机服务标签信息失败！", e);
        }
        return DriverServiceTagDTO.getDefault();
    }
}
