package com.ly.travel.shared.mobility.integration.utils;

import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


@Slf4j
public class AsrCoreConfigCenterUtils {

    private static String getValue(String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    private static String getValue(String appUk, String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(appUk, key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    public static int getSfcOrderBeforeDays() {
        int days = 90;
        try {
            String str = getValue("sfc_order_before_days");

            if (StringUtils.isNotBlank(str)) {
                days = Integer.parseInt(str);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return days;
    }


}
