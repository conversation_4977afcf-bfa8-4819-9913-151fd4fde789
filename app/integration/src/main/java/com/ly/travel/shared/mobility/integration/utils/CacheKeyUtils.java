package com.ly.travel.shared.mobility.integration.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class CacheKeyUtils {
    @Value("${sof-env}")
    private String env;

    private static String staticEnv;

    @PostConstruct
    public void init() {
        CacheKeyUtils.staticEnv = this.env;
    }

    /**
     * 根据账号生成检查缓存键
     *
     * @param account 用户账号
     * @return 生成的缓存键字符串
     */
    public static String getInspectorCacheKey(String account) {
        return String.format("inspector:%s:%s", staticEnv, account);
    }

    public static String getAsrOrderSyncCacheKey(String account) {
        return String.format("asr:order:sync:%s:%s", staticEnv, account);
    }
}
