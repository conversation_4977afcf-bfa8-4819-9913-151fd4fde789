package com.ly.travel.shared.mobility.integration.client.ordercore;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.ordercore.facade.OrderFacade;
import com.ly.travel.car.ordercore.facade.request.SimpleOrderDetailListRequest;
import com.ly.travel.car.ordercore.facade.response.SimpleOrderDetailListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description: 订单查询
 * @Author: jay.he
 * @Date: 2025-08-25 16:35
 * @Version: 1.0
 **/
@Slf4j
@Service
public class OrderCoreClient {

    private static final String LOG_PREFIX = "OrderCoreClient";

    @Resource
    private OrderFacade carOrderFacade;

    public SimpleOrderDetailListResponse simpleDetailList(SimpleOrderDetailListRequest request) {
        log.info("{} simpleDetailList request:{}", LOG_PREFIX, JSON.toJSONString(request));
        SimpleOrderDetailListResponse resp = carOrderFacade.simpleDetailList(request);
        log.info("{} simpleDetailList response:{}", LOG_PREFIX, JSON.toJSONString(resp));
        return resp;
    }
}
