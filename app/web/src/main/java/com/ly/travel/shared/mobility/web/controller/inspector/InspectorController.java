package com.ly.travel.shared.mobility.web.controller.inspector;

import com.ly.sof.utils.page.PageList;
import com.ly.travel.shared.mobility.biz.model.req.asrorder.AsrOrderInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.asrorder.UploadSoundInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorImproveInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorLoginReq;
import com.ly.travel.shared.mobility.biz.model.resp.AsrCoreBaseResp;
import com.ly.travel.shared.mobility.biz.model.resp.AsrOrderInfoResp;
import com.ly.travel.shared.mobility.biz.model.resp.inspector.InspectorJumpPageResp;
import com.ly.travel.shared.mobility.biz.model.resp.inspector.InspectorLoginResp;
import com.ly.travel.shared.mobility.biz.service.InspectorService;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;

@RestController
@RequestMapping("inspector")
public class InspectorController {
    @Resource
    private InspectorService inspectorService;

    /**
     * 登录
     *
     * @return
     */
    @PostMapping("login")
    public AsrCoreBaseResp<InspectorLoginResp> login(@RequestBody InspectorLoginReq req) {
        return AsrCoreBaseResp.ok(inspectorService.login(req));
    }

    /**
     * 完善信息
     *
     * @return
     */
    @PostMapping("improveInfo")
    public AsrCoreBaseResp improveInfo(@RequestBody InspectorImproveInfoReq req) {
        inspectorService.improveInfo(req);
        return AsrCoreBaseResp.ok();
    }

    /**
     * 查询跳转地址
     *
     * @return
     */
    @PostMapping("queryJumpPage")
    public AsrCoreBaseResp<InspectorJumpPageResp> queryJumpPage() {
        return AsrCoreBaseResp.ok(inspectorService.queryJumpPage());
    }

    /**
     * 获取当前用户信息
     *
     * @return
     */
    @PostMapping("getUserInfo")
    public AsrCoreBaseResp<InspectorAccountDTO> getUserInfo() {
        return AsrCoreBaseResp.ok(inspectorService.getUserInfo(false));
    }

    /**
     * asr 订单列表
     *
     * @param req
     * @return
     */
    @PostMapping("queryAsrOrderInfoPage")
    public AsrCoreBaseResp<PageList<AsrOrderInfoResp>> queryAsrOrderInfoPage(@RequestBody AsrOrderInfoReq req) {
        return AsrCoreBaseResp.ok(inspectorService.queryAsrOrderInfoPage(req));
    }

    /**
     * 保存上传信息
     *
     * @param req
     * @return
     */
    @PostMapping("uploadSoundInfo")
    public AsrCoreBaseResp uploadSoundInfo(@RequestParam("file") CommonsMultipartFile file, UploadSoundInfoReq req) {
        inspectorService.uploadSoundInfo(file, req);
        return AsrCoreBaseResp.ok();
    }
}
