package com.ly.travel.shared.mobility.web.controller;

import com.ly.travel.shared.mobility.biz.service.InspectorService;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/25
 */
@RestController
@RequestMapping("myInfo")
public class MyInfoController {

    @Resource
    private InspectorService inspectorService;

    @RequestMapping("queryMyInfo")
    public InspectorAccountDTO queryMyInfo(){
        return inspectorService.getUserInfo(true);
    }
}
