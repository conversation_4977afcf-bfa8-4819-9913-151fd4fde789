<template>
  <div class="profile-container">
    <div class="profile-header">
      <h2 class="page-title">
        <i class="el-icon-user"></i>
        个人信息
      </h2>
    </div>

    <div class="profile-content" v-loading="loading">
      <el-card class="profile-card">
        <div slot="header" class="card-header">
          <span class="card-title">基本信息</span>
        </div>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">用户账号：</label>
              <span class="info-value">{{ userInfo.userAccount || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">用户名称：</label>
              <span class="info-value">{{ userInfo.name || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">手机号码：</label>
              <span class="info-value">{{ userInfo.mobileNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">账号状态：</label>
              <el-tag :type="getStatusType(userInfo.status)" size="small">
                {{ getStatusText(userInfo.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <div class="info-item">
              <label class="info-label">邮寄地址：</label>
              <span class="info-value">{{ userInfo.mailAddress || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="profile-card">
        <div slot="header" class="card-header">
          <span class="card-title">收件信息</span>
        </div>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">收件人姓名：</label>
              <span class="info-value">{{ userInfo.mailName || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">收件人手机号：</label>
              <span class="info-value">{{ userInfo.mailPhone || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="profile-card">
        <div slot="header" class="card-header">
          <span class="card-title">收款信息</span>
        </div>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">收款方式：</label>
              <span class="info-value">{{ getPaymentMethodText(userInfo.paymentMethod) }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">收款人姓名：</label>
              <span class="info-value">{{ userInfo.paymentName || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="24">
            <div class="info-item">
              <label class="info-label">收款卡号：</label>
              <span class="info-value">{{ formatCardNo(userInfo.paymentCardNo) }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="profile-card">
        <div slot="header" class="card-header">
          <span class="card-title">更新记录</span>
        </div>
        
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">更新人：</label>
              <span class="info-value">{{ userInfo.updateBy || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label class="info-label">更新时间：</label>
              <span class="info-value">{{ userInfo.updateTime || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectorProfile',
  data() {
    return {
      loading: false,
      userInfo: {}
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo() {
      this.loading = true
      try {
        const param = {
          url: 'inspector/getUserInfo',
          method: 'POST'
        }
        const res = await this.$http(param)
        if (res.success) {
          this.userInfo = res.data || {}
        } else {
          this.$message.error(res.msg || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败')
      } finally {
        this.loading = false
      }
    },
    getStatusText(status) {
      const statusMap = {
        1: '正常',
        2: '待完善',
        3: '待发货',
        4: '已发货',
        0: '无效'
      }
      return statusMap[status] || '未知'
    },
    getStatusType(status) {
      const typeMap = {
        1: 'success',
        2: 'warning',
        3: 'info',
        4: 'success',
        0: 'danger'
      }
      return typeMap[status] || 'info'
    },
    getPaymentMethodText(method) {
      const methodMap = {
        1: '支付宝',
        2: '银行卡'
      }
      return methodMap[method] || '-'
    },
    formatCardNo(cardNo) {
      if (!cardNo) return '-'
      // 隐藏中间部分卡号，只显示前4位和后4位
      if (cardNo.length > 8) {
        return cardNo.substring(0, 4) + '****' + cardNo.substring(cardNo.length - 4)
      }
      return cardNo
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  .profile-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }

  .profile-content {
    .profile-card {
      margin-bottom: 20px;

      .card-header {
        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
        }
      }

      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        .info-label {
          width: 120px;
          color: #606266;
          font-weight: 500;
          flex-shrink: 0;
        }

        .info-value {
          color: #303133;
          flex: 1;
          word-break: break-all;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .profile-container {
    .profile-content {
      .profile-card {
        .info-item {
          flex-direction: column;
          align-items: flex-start;

          .info-label {
            width: auto;
            margin-bottom: 4px;
          }
        }
      }
    }
  }
}
</style>
