<template>
    <div class="settings-container">
        <!-- 左侧导航 -->
        <div class="sidebar">
            <div class="nav-item active">基本设置</div>
        </div>

        <!-- 右侧表单 -->
        <div class="main-content">
            <el-form
                    label-width="120px"
                    :model="formData"
                    size="small"
                    ref="formData"
                    class="user-form"
            >
                <el-form-item label="手机号" prop="mobileNo">
                    <el-input v-model="formData.mobileNo" disabled></el-input>
                </el-form-item>

                <el-form-item label="密码" prop="password">
                    <el-input v-model="formData.password" show-password></el-input>
<!--                    <el-button type="text" @click="handleModifyPassword" style="margin-left: 10px;">修改密码</el-button>-->
                </el-form-item>

                <el-form-item label="收件人姓名" prop="mailName">
                    <el-input v-model="formData.mailName"></el-input>
                </el-form-item>

                <el-form-item label="收件人手机号" prop="mailPhone">
                    <el-input v-model="formData.mailPhone"></el-input>
                </el-form-item>

                <el-form-item label="邮寄地址" prop="mailAddress">
                    <el-input v-model="formData.mailAddress" maxlength="64"></el-input>
                </el-form-item>

                <el-form-item label="收款方式" prop="paymentMethod">
                    <el-select v-model="formData.paymentMethod" placeholder="请选择收款方式">
                        <el-option label="支付宝" :value="1"></el-option>
                        <el-option label="银行卡" :value="2"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="收款人姓名" prop="paymentName" v-if="formData.paymentMethod === 2">
                    <el-input v-model="formData.paymentName"></el-input>
                </el-form-item>

                <el-form-item label="卡号" prop="paymentCardNo">
                    <el-input v-model="formData.paymentCardNo"></el-input>
                </el-form-item>

                <!-- 提交按钮 -->
<!--                <el-form-item>-->
<!--                    <el-button type="primary" @click="handleSubmit">更新信息</el-button>-->
<!--                </el-form-item>-->
            </el-form>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                mobileNo: '',
                password: '',
                mobile: '',
                mailName: '',
                mailPhone: '',
                mailAddress: '',
                paymentMethod: 2,
                paymentName: '',
                paymentCardNo: ''
            }
        }
    },
    created() {
        this.getUserInfo()
    },
    methods: {
        async getUserInfo() {
            this.loading = true
            try {
                const param = {
                    url: 'inspector/getUserInfo',
                    method: 'POST'
                }
                const res = await this.$http(param)
                if (res.success) {
                    this.formData = res.data || {}
                } else {
                    this.$message.error(res.msg || '获取用户信息失败')
                }
            } catch (error) {
                console.error('获取用户信息失败:', error)
                this.$message.error('获取用户信息失败')
            } finally {
                this.loading = false
            }
        },
        handleModifyPassword() {
            this.$message.info('')
        },

        handleSubmit() {
            this.$refs.formData.validate(valid => {
                if (valid) {
                    // 模拟提交
                    this.$message.success('信息更新成功')
                } else {
                    this.$message.error('请填写完整信息')
                }
            })
        }
    }
}
</script>

<style scoped>
.settings-container {
    display: flex;
    height: 100vh;
    background-color: #f5f7fa;
}

.sidebar {
    width: 200px;
    background-color: #fff;
    border-right: 1px solid #e6e6e6;
    padding: 20px 0;
}

.nav-item {
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 1px solid #eee;
}

.nav-item.active {
    background-color: #e6f7ff;
    color: #007aff;
    border-left: 3px solid #007aff;
}

.main-content {
    flex: 1;
    padding: 30px;
    background-color: #fff;
}

.user-form {
    max-width: 600px;
}

.el-form-item__label {
    font-weight: 500;
    color: #333;
}

.el-input {
    width: 300px;
}

.el-button--primary {
    background-color: #007aff;
    border-color: #007aff;
    color: white;
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 4px;
}

/* 确保按钮不被遮挡 */
.el-form-item .el-button {
    margin-top: 10px;
}
</style>
