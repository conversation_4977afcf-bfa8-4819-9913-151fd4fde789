<template>
  <div v-cloak class="order-list-container">
    <div class="page-header">
      <h2 class="page-title">
        <i class="el-icon-document"></i>
        订单列表
      </h2>
    </div>

    <el-form :inline="true" :model="searchForm" class="demo-form-inline" size="mini" label-width="130px">

      <el-form-item label="订单ID">
        <el-input v-model="searchForm.orderSerialNo" clearable placeholder="请输入订单ID"
                  maxlength="100"></el-input>
      </el-form-item>
      <el-form-item label="车牌号">
        <el-input v-model="searchForm.plateNo" clearable placeholder="请输入车牌号">
        </el-input>
      </el-form-item>
      <el-form-item label="行程时间">
        <el-row type="flex">
          <el-date-picker
              v-model="searchForm.tripStartTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间">
          </el-date-picker>
           至
          <el-date-picker
              v-model="searchForm.tripEndTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="23:59:59"
              placeholder="结束时间">
          </el-date-picker>
        </el-row>

      </el-form-item>

      <el-form-item label="上传状态">
        <el-select v-model="searchForm.uploadStatus" placeholder="请选择" clearable>
          <el-option label="待上传" :value="0"/>
          <el-option label="已上传" :value="1"/>
          <el-option label="不合格" :value="2"/>
          <el-option label="合格" :value="3"/>
        </el-select>
      </el-form-item>
      <el-form-item label="审核状态">
        <el-select v-model="searchForm.auditStatus" placeholder="请选择" clearable>
          <el-option label="待审核" :value="0"/>
          <el-option label="已审核" :value="1"/>
        </el-select>
      </el-form-item>
      <el-form-item label="订单类型">
        <el-select v-model="searchForm.orderType" placeholder="请选择" clearable>
          <el-option
            v-for="item in orderTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-row type="flex" justify="center" style="margin-bottom: 10px">
        <div style="margin-left: 10px">
          <el-button size="mini" plain icon="el-icon-refresh" @click="resetClick">重置
          </el-button>
        </div>
        <div style="margin-left: 10px">
          <el-button size="mini" type="primary" plain icon="el-icon-search" @click="queryClick">查询
          </el-button>
        </div>
      </el-row>
    </el-form>


    <tablePage ref="multipleTable" :request-param="{
                url: 'inspector/queryAsrOrderInfoPage',
                method: 'POST',
                data: this.searchForm
            }">
      <el-table-column prop="orderSerialNo" align="center" label="订单ID" />
      <el-table-column align="center" label="行程时间" width="280">
        <template slot-scope="{row}">
          {{ formatDate(row.tripStartTime) }}----{{ formatDate(row.tripEndTime) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="上传时间">
        <template slot-scope="{row}">
          {{ formatDate(row.uploadTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="plateNo" align="center" label="车牌号">
      </el-table-column>
      <el-table-column prop="uploadStatusDesc" align="center" label="上传状态" width="150">
      </el-table-column>
      <el-table-column prop="auditStatusDesc" align="center" label="审核状态" width="150">
      </el-table-column>
      <el-table-column align="center" label="司机评分" width="120">
        <template slot-scope="{row}">
          <el-rate
            v-if="row.driverScore"
            :value="row.driverScore"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}">
          </el-rate>
          <span v-else>--</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="300" fixed="right">
        <template slot-scope="{row}">
          <el-button v-if="row.auditStatus ===0" type="primary" size="mini" plain icon="el-icon-upload"
                     @click="uploadClick(row)">
            {{ row.fileUrl ? '重新上传' : '上传' }}
          </el-button>
        </template>
      </el-table-column>
    </tablePage>
    <uploadModal :edit-info="editInfo" :is-show="showUploadModal" v-if="showUploadModal"
               @callbackclose="closeUploadModal"></uploadModal>
  </div>
</template>


<script>
import uploadModal from './components/uploadModal'
import tablePage from '@/components/tablePage'
import { formatDate } from '@/utils/index'

const defaultStartTime = new Date();
defaultStartTime.setDate(defaultStartTime.getDate() - 90);

let orgSearchForm = {
  orderSerialNo: '',
  tripStartTime: formatDate(defaultStartTime,'yyyy-MM-dd 00:00:00'),
  tripEndTime: formatDate(new Date(),'yyyy-MM-dd 23:59:59'),
  plateNo: '',
  uploadStatus: '',
  auditStatus: '',
  orderType: ''
}


export default {
  components: {
    uploadModal,
    tablePage
  },
  data() {
    return {
      searchForm: this.deepCopy(orgSearchForm),
      editInfo: {},
      showUploadModal: false,
      orderTypeOptions: []
    }
  },

  created() {
    this.loadOrderTypeOptions()
  },
  mounted() {
    this.queryClick()
  },
  filters: {},
  methods: {
    loadOrderTypeOptions() {
      this.$http({
        url: 'inspector/orderTypeOptions',
        method: 'POST',
        data: {}
      }).then((res) => {
        if (res.success) {
          this.orderTypeOptions = res.data
        }
      }).catch(err => {
        console.log(err)
      })
    },
    uploadClick(row){
      this.editInfo = this.deepCopy(row)
      this.showUploadModal = true
    },
    //重置
    resetClick() {
      this.searchForm = this.deepCopy(orgSearchForm)
    },
    //查询
    queryClick() {
      this.$refs.multipleTable.searchData(1)
    },
    closeUploadModal(result) {
      this.showUploadModal = false
      if (result) {
        this.$refs.multipleTable.searchData()
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.order-list-container {
  .page-header {
    margin-bottom: 20px;

    .page-title {
      font-size: 20px;
      color: #303133;
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409EFF;
      }
    }
  }
  .demo-form-inline {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;

    .el-form-item {
      margin-bottom: 16px;
    }

    .el-button {
      margin: 0 5px;
    }
  }

  // 表格样式优化
  ::v-deep .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .el-table__header {
      background-color: #f8f9fa;

      th {
        background-color: #f8f9fa !important;
        color: #303133;
        font-weight: 600;
      }
    }

    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 分页样式
  ::v-deep .el-pagination {
    text-align: center;
    margin-top: 20px;
    padding: 20px 0;
  }
}
</style>
