<template>
  <el-dialog class="edit_pop_div" :visible="isShow" title="上传录音"
             :close-on-click-modal="false" @close="cancelClick"
             :append-to-body="true" width="50%" center>
    <el-form label-width="140px"
             :model="formData"
             size="small"
             ref="formData"
    >
      <el-form-item label="订单号" prop="orderSerialNo"
                    :rules="[{ required: true, message: '离线指标编号不能为空' }]">
        {{ formData.orderSerialNo }}
      </el-form-item>
      <el-form-item label="订单类型" prop="orderType">
        {{ getOrderTypeLabel(formData.orderType) }}
      </el-form-item>
      <el-form-item label="上传录音" prop="fileList"
                    :rules="[{ required: true, message: '录音内容不能为空' }]">
        <el-upload name="file"
                   drag
                   action="../upload/upload"
                   :file-list="formData.fileList"
                   :limit="1"
                   :auto-upload="false"
                   accept=".zip,.rar,.avi,.mp3,.wav,.flac,.aac,.m4a,.wma,.ogg,.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm"
                   :on-success="onUploadSuccess"
                   :on-change="changeFile"
                   :on-exceed="onExceed"
                   :on-error="onError"
                   :before-upload="beforeUpload"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">支持扩展名:.zip,.rar,.avi,.mp3,.wav,.flac,.aac,.m4a,.wma,.ogg,.mp4,.avi,.mov,.wmv,.flv,.mkv,.webm</div>
        </el-upload>
      </el-form-item>

      <el-form-item label="请选择司机服务">
        <el-checkbox-group v-model="formData.serviceTagList">
          <el-checkbox
            v-for="tag in serviceTagOptions"
            :key="tag.value"
            :label="tag.value">
            {{ tag.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="服务点评">
        <el-input
          v-model="formData.serviceReview"
          type="textarea"
          :rows="4"
          placeholder="请输入服务点评"
          maxlength="500"
          show-word-limit>
        </el-input>
      </el-form-item>

      <el-form-item label="司机服务评分">
        <el-rate
          v-model="formData.driverScore"
          :max="5"
          show-text
          :texts="['极差', '失望', '一般', '满意', '惊喜']">
        </el-rate>
      </el-form-item>

    </el-form>

    <span slot="footer" class="dialog-footer">
       <el-button type="default" size="mini" @click="cancelClick">
            取消
        </el-button>
        <el-button type="primary" :loading="loadingSave" size="mini" @click="submitClick">
            保存
        </el-button>
    </span>
    <fullscreen-progress :percentage="uploadPercentage" :visible="progressVisible" />
  </el-dialog>

</template>

<script>

import {isEmpty} from "@/utils/util";
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import FullscreenProgress from "@/components/fullscreenProgress/index.vue";

let orgFormData = {
  orderSerialNo: '',
  fileList: [],
  serviceTagList: [],
  serviceReview: '',
  driverScore: 5
}

export default {
  components: {
    FullscreenProgress,
    ElImageViewer
  },
  props: {
    editInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isShow: {
      type: Boolean,
      default: false
    },
  },

  data: function () {
    return {
      formData: this.deepCopy(orgFormData),
      loadingSave: false,
      progressVisible: false,
      uploadPercentage: 0,
      serviceTagOptions: [],
      orderTypeOptions: [
        {value: 11, label: "网约车: 预约用车"},
        {value: 12, label: "网约车: 接机"},
        {value: 13, label: "网约车: 送机"},
        {value: 14, label: "网约车: 接站"},
        {value: 15, label: "网约车: 送站"},
        {value: 19, label: "网约车: 即时专车"},
        {value: 80, label: "顺风车"}
      ]
    }
  },
  created() {
    if (!isEmpty(this.editInfo)) {
      this.formData = {...this.deepCopy(orgFormData), ...this.editInfo}
      // 加载服务标签选项
      if (this.editInfo.orderType) {
        this.loadServiceTags(this.editInfo.orderType)
      }
    } else {
      this.formData = this.deepCopy(orgFormData)
    }
  },
  mounted() {
  },
  methods: {
    getOrderTypeLabel(orderType) {
      const option = this.orderTypeOptions.find(item => item.value === orderType)
      return option ? option.label : '未知类型'
    },
    loadServiceTags(orderType) {
      this.$http({
        url: 'inspector/serviceTags',
        method: 'POST',
        data: { orderType: orderType }
      }).then((res) => {
        if (res.success) {
          this.serviceTagOptions = res.data
        }
      }).catch(err => {
        console.log(err)
      })
    },
    //开始加载
    startLoading() {
      this.$nextTick(() => {
        this.loading = this.$loading({
          lock: true,
          text: "上传中",
          spinner: "el-icon-loading",
          background: "rgba(0, 0, 0, 0.8)", //调节透明度
        });
      })

    },
    //结束加载
    endLoading() {
      if (this.loading) {
        this.loading.close();
      }
    },
    beforeUpload(file) {
      // 允许上传的文件格式列表
      let acceptList =["zip","rar","mp3", "wav", "flac", "aac", "m4a", "wma", "ogg", "mp4", "avi", "mov", "wmv", "flv", "mkv", "webm"]
      // 根据文件名获取文件的后缀名
      let fileType = file.name.split('.').pop().toLowerCase()
      if (!acceptList.includes(fileType)) {
        this.$message.error('上传的格式的文件 !');
        return false
      }
      return true; // 允许上传
    },
    onError(err, file, fileList) {
      console.log(err)
      this.$message.error('上传文件失败')
    },
    onExceed(files, fileList) {
      this.$message.error('一次只能上传一个文件')
    },
    changeFile(file, fileList) {
      this.formData.fileList = fileList
      this.$refs.formData.clearValidate('fileList')
    },
    onUploadSuccess(result, file, fileList) {
      this.$refs.file.clearFiles()
      if (result.success) {
        this.$message.success('上传成功')
        this.formData.fileList = [result.data.fileUrl]
        return
      }
      this.$message.error(`上传失败,失败原因:${result.msg}`)
    },
    submitClick() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.loadingSave = true

          // 创建FormData对象
          const formData = new FormData()
          formData.append('orderSerialNo', this.formData.orderSerialNo)
          formData.append('serviceTag', this.formData.serviceTagList.join(','))
          formData.append('serviceReview', this.formData.serviceReview || '')

          // 添加所有文件

          formData.append('file', this.formData.fileList[0].raw)

          this.progressVisible = true
          this.uploadPercentage = 0

          let param = {
            url: 'inspector/uploadSoundInfo',
            method: 'POST',
            data: formData,
            headers: {
              'Content-Type': 'multipart/form-data'
            },
            onUploadProgress: (progressEvent) => {
              // 计算上传进度
              let percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
              if (percentage >= 100) {
                percentage = 99
              }
              this.uploadPercentage = percentage
            }
          }

          //this.startLoading()

          this.$http(param).then((res) => {
            if (res.success) {
              this.$message.success('上传成功!')
            } else {
              this.$message.error(res.msg)
            }
            this.loadingSave = false
            //this.endLoading()
            this.progressVisible =false
            this.$emit('callbackclose', 'success')
          }).catch(err => {
            console.log(err);
            this.$message.error('上传失败,请稍后重试')
            this.loadingSave = false
            //this.endLoading()
            this.progressVisible =false
          })
        }
      });

    },
    //取消
    cancelClick() {
      this.$emit('callbackclose')
    }
  }
}
</script>

<style scoped>
</style>
