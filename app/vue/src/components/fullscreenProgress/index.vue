<!-- FullscreenProgress.vue -->
<template>
  <transition name="fade">
    <div v-if="visible" class="fullscreen-progress-wrapper">
      <div class="progress-container">
        <el-progress
            type="circle"
            :percentage="percentage"
            :width="120"
            :stroke-width="4"
            :color="colors"
        ></el-progress>
        <div class="progress-info">
          <h3>{{ title }}</h3>
          <p>{{ message }}</p>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'FullscreenProgress',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    percentage: {
      type: Number,
      default: 0
    },
    title: {
      type: String,
      default: '正在上传'
    },
    message: {
      type: String,
      default: '请稍候...'
    }
  },
  computed: {
    colors() {
      return [
        { color: '#f56c6c', percentage: 20 },
        { color: '#e6a23c', percentage: 40 },
        { color: '#5cb87a', percentage: 60 },
        { color: '#1989fa', percentage: 80 },
        { color: '#6f7ad3', percentage: 100 }
      ]
    }
  }
}
</script>

<style scoped>
.fullscreen-progress-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.progress-container {
  text-align: center;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.progress-info {
  margin-top: 20px;
}

.progress-info h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 18px;
}

.progress-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>
