# 质量抽检系统使用说明

## 系统概述

质量抽检系统是一个基于Vue.js和Spring Boot的Web应用，为检查员提供了一个现代化的工作界面，包含个人信息管理和订单列表功能。

## 功能特性

### 1. 系统框架
- **现代化界面设计**：采用Element UI组件库，提供美观的用户界面
- **响应式布局**：支持桌面和移动设备访问
- **统一导航**：左侧菜单栏提供快速导航

### 2. 主要功能模块

#### 个人信息管理 (`/inspector/profile`)
- 查看个人基本信息（账号、姓名、手机号等）
- 查看收件信息（收件人姓名、手机号、邮寄地址）
- 查看收款信息（收款方式、收款人、收款卡号）
- 查看更新记录（更新人、更新时间）
- 账号状态显示（正常、待完善、待发货等）

#### 订单列表管理 (`/inspector/orders`)
- 订单查询功能（支持订单ID、车牌号、行程时间等条件）
- 订单状态管理（上传状态、审核状态）
- 文件上传功能
- 分页显示订单列表

## 技术架构

### 前端技术栈
- **Vue.js 2.x**：前端框架
- **Element UI**：UI组件库
- **Vue Router**：路由管理
- **Axios**：HTTP请求库
- **SCSS**：样式预处理器

### 后端技术栈
- **Spring Boot**：后端框架
- **MyBatis Plus**：ORM框架
- **Redis**：缓存存储
- **Maven**：项目管理工具

## 项目结构

```
app/
├── vue/                          # 前端项目
│   ├── src/
│   │   ├── layout/
│   │   │   ├── InspectorLayout.vue    # 主框架布局组件
│   │   │   └── index.vue              # 原有布局组件
│   │   ├── views/
│   │   │   ├── inspectorProfile/      # 个人信息页面
│   │   │   ├── asrOrderList/          # 订单列表页面
│   │   │   ├── inspectorLogin/        # 登录页面
│   │   │   └── inspectorHome/         # 首页跳转
│   │   ├── router/
│   │   │   └── index.js               # 路由配置
│   │   └── utils/                     # 工具类
├── web/                          # Web控制层
│   └── src/main/java/.../controller/
│       └── InspectorController.java   # 检查员控制器
├── biz/                          # 业务逻辑层
│   └── src/main/java/.../service/
│       └── InspectorService.java      # 检查员服务
└── integration/                  # 集成层
    └── src/main/java/.../dto/
        └── InspectorAccountDTO.java   # 用户信息DTO
```

## 开发环境搭建

### 前端开发
```bash
cd app/vue
npm install
npm run dev
```

### 后端开发
```bash
cd app
mvn clean install
mvn spring-boot:run
```

## 新增功能说明

### 1. 主框架布局 (InspectorLayout.vue)
- 顶部标题栏显示系统名称"质量抽检系统"
- 右上角显示用户欢迎信息和退出按钮
- 左侧菜单包含"个人信息"和"订单列表"两个入口
- 响应式设计，支持移动端访问

### 2. 个人信息页面 (inspectorProfile/index.vue)
- 分卡片展示用户信息，包括：
  - 基本信息：账号、姓名、手机号、状态
  - 收件信息：收件人姓名、手机号、邮寄地址
  - 收款信息：收款方式、收款人、收款卡号（脱敏显示）
  - 更新记录：更新人、更新时间
- 状态标签颜色区分不同账号状态
- 卡号脱敏处理保护隐私

### 3. API接口扩展
- 新增 `POST /inspector/getUserInfo` 接口
- 返回当前登录用户的详细信息
- 自动隐藏密码等敏感信息
- 支持token验证和缓存续期

### 4. 路由配置更新
- 新增 `/inspector` 路由组，使用InspectorLayout布局
- 个人信息路由：`/inspector/profile`
- 订单列表路由：`/inspector/orders`
- 保持原有路由兼容性

## 使用流程

1. **登录系统**：访问 `/login` 页面进行登录
2. **自动跳转**：登录成功后自动跳转到订单列表页面
3. **导航使用**：通过左侧菜单在个人信息和订单列表间切换
4. **查看信息**：在个人信息页面查看详细的用户信息
5. **管理订单**：在订单列表页面进行订单查询和管理操作

## 注意事项

1. **权限验证**：所有页面都需要登录后才能访问
2. **数据安全**：敏感信息（如密码、卡号）进行了脱敏处理
3. **缓存管理**：用户信息支持Redis缓存，提高访问性能
4. **响应式设计**：界面适配不同屏幕尺寸的设备

## 后续扩展建议

1. **权限管理**：可以根据用户角色显示不同的菜单项
2. **消息通知**：添加系统消息和通知功能
3. **数据导出**：支持订单数据的Excel导出
4. **操作日志**：记录用户的重要操作日志
5. **移动端优化**：进一步优化移动端用户体验
