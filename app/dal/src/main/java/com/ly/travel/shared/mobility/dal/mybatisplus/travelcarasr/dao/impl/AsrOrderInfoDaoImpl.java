package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ly.sof.utils.page.PageQuery;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrOrderInfoDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper.AsrOrderInfoMapper;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrOrderInfoDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * asr订单信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/19 10:05
 */
@Service
public class AsrOrderInfoDaoImpl extends ServiceImpl<AsrOrderInfoMapper, AsrOrderInfoDO> implements AsrOrderInfoDao {
    @Value("${sof-env}")
    private String env;

    @Override
    public Page<AsrOrderInfoDO> queryPage(PageQuery pageQuery, String mobileNo, String orderSerialNo, String plateNo, Integer uploadStatus,
                                          Integer auditStatus, Date tripStartTime, Date tripEndTime, Integer orderType) {
        Page<AsrOrderInfoDO> page = new Page<>(pageQuery.getPage(), pageQuery.getPageSize());
        LambdaQueryWrapper<AsrOrderInfoDO> wrapper = new LambdaQueryWrapper<AsrOrderInfoDO>()
                .eq(AsrOrderInfoDO::getMobileNo, mobileNo)
                .eq(StringUtils.isNotBlank(orderSerialNo), AsrOrderInfoDO::getOrderSerialNo, orderSerialNo)
                .eq(uploadStatus != null, AsrOrderInfoDO::getUploadStatus, uploadStatus)
                .eq(auditStatus != null, AsrOrderInfoDO::getAuditStatus, auditStatus)
                .ge(tripStartTime != null, AsrOrderInfoDO::getTripStartTime, tripStartTime)
                .le(tripEndTime != null, AsrOrderInfoDO::getTripEndTime, tripEndTime)
                .eq(StringUtils.isNotBlank(plateNo), AsrOrderInfoDO::getPlateNo, plateNo)
                .eq(orderType != null && orderType > 0, AsrOrderInfoDO::getOrderType, orderType)
                .eq(AsrOrderInfoDO::getEnv, env)
                .orderByDesc(AsrOrderInfoDO::getTripStartTime);
        return baseMapper.selectPage(page, wrapper);
    }

    @Override
    public AsrOrderInfoDO queryByOrderSerialNo(String orderSerialNo) {
        LambdaQueryWrapper<AsrOrderInfoDO> wrapper = new LambdaQueryWrapper<AsrOrderInfoDO>()
                .eq(AsrOrderInfoDO::getOrderSerialNo, orderSerialNo)
                .eq(AsrOrderInfoDO::getEnv, env)
                .orderByDesc(AsrOrderInfoDO::getTripStartTime);
        return getOne(wrapper, false);
    }



    @Override
    public List<AsrOrderInfoDO> queryExists(String mobileNo) {
        LambdaQueryWrapper<AsrOrderInfoDO> wrapper = new LambdaQueryWrapper<AsrOrderInfoDO>()
                .eq(AsrOrderInfoDO::getMobileNo, mobileNo)
                .eq(AsrOrderInfoDO::getEnv, env)
                .select(AsrOrderInfoDO::getOrderSerialNo);
        return baseMapper.selectList(wrapper);
    }

}
