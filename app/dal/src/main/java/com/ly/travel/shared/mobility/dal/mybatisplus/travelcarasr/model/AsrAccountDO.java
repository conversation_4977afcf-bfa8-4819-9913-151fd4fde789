package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 语音识别结果记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/25 16:43
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("asr_account")
public class AsrAccountDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String name;

    /**
     * 邮寄地址
     */
    @TableField("mail_address")
    private String mailAddress;

    /**
     * 收件人姓名
     */
    @TableField("mail_name")
    private String mailName;

    /**
     * 收件人手机号
     */
    @TableField("mail_phone")
    private String mailPhone;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 资料采集方式(0:设备采集 1:手机自采)
     */
    @TableField("collect_type")
    private Integer collectType;

    /**
     * 收款方式,1:支付宝,2:银行卡
     */
    @TableField("payment_method")
    private Integer paymentMethod;

    /**
     * 收款卡号
     */
    @TableField("payment_card_no")
    private String paymentCardNo;

    /**
     * 收款户名
     */
    @TableField("payment_name")
    private String paymentName;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 操作人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 环境
     */
    @TableField(fill = FieldFill.INSERT)
    private String env;


}
