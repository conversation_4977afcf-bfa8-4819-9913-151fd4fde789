package com.ly.travel.shared.mobility.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrAccountDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrAccountDO;
import com.ly.travel.shared.mobility.integration.enums.AccountStatusEnum;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/25
 */
@Service
public class AccountService {

    @Resource
    private AsrAccountDao accountDao;
    @Value("${sof-env}")
    private String env;


    public AsrAccountDO getAccount(String mobile){
        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getMobile, mobile).eq(AsrAccountDO::getEnv, env);
        wrapper.ne(AsrAccountDO::getStatus, AccountStatusEnum.INVALID.getStatus());
        List<AsrAccountDO> dbList = accountDao.getBaseMapper().selectList(wrapper);
        return !dbList.isEmpty() ? dbList.get(0) : null;
    }

    public List<AsrAccountDO> getAccountList(){
        LambdaQueryWrapper<AsrAccountDO> wrapper = Wrappers.<AsrAccountDO>lambdaQuery().eq(AsrAccountDO::getEnv, env);
        return accountDao.getBaseMapper().selectList(wrapper);
    }
}
