package com.ly.travel.shared.mobility.biz.model.resp;

import lombok.Data;

import java.util.Date;

@Data
public class AsrOrderInfoResp {
    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 行程开始时间
     */
    private Date tripStartTime;

    /**
     * 行程结束时间
     */
    private Date tripEndTime;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 上传文件地址
     */
    private String fileUrl;

    /**
     * 上传状态
     */
    private Integer uploadStatus;
    /**
     * 上传状态描述
     */
    private String uploadStatusDesc;

    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核状态描述
     */
    private String auditStatusDesc;

    /**
     * 订单类型
     */
    private Integer orderType;
}
