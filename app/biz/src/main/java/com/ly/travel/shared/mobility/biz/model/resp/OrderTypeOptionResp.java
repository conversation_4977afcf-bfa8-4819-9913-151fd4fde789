package com.ly.travel.shared.mobility.biz.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderTypeOptionResp {
    /**
     * 订单类型值
     */
    private Integer value;

    /**
     * 订单类型标签
     */
    private String label;

    /**
     * 订单类型
     *
     * @return
     */
    public static List<OrderTypeOptionResp> getOrderTypeOptions() {
        List<OrderTypeOptionResp> options = new ArrayList<>();
        options.add(new OrderTypeOptionResp(11, "网约车: 预约用车"));
        options.add(new OrderTypeOptionResp(12, "网约车: 接机"));
        options.add(new OrderTypeOptionResp(13, "网约车: 送机"));
        options.add(new OrderTypeOptionResp(14, "网约车: 接站"));
        options.add(new OrderTypeOptionResp(15, "网约车: 送站"));
        options.add(new OrderTypeOptionResp(19, "网约车: 即时专车"));
        options.add(new OrderTypeOptionResp(80, "顺风车"));
        return options;
    }
}
