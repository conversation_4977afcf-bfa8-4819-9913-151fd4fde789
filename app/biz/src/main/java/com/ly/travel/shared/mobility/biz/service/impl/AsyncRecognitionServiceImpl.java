package com.ly.travel.shared.mobility.biz.service.impl;

import com.ly.sof.utils.mapping.JacksonUtils;
import com.ly.tcbase.config.AppProfile;
import com.ly.tcbase.websocket.client.WebSocketClient;
import com.ly.tcbase.websocket.handshake.ServerHandshake;
import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import com.ly.travel.shared.mobility.biz.model.vo.meta.UploadFileInfoVO;
import com.ly.travel.shared.mobility.biz.model.vo.recognition.AsrRecognitionResult;
import com.ly.travel.shared.mobility.biz.service.AsyncRecognitionService;
import com.ly.travel.shared.mobility.biz.utils.FileUtils;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper.AsrRecognitionRecordMapper;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrRecognitionRecordDO;
import com.ly.travel.shared.mobility.integration.utils.S3ClientApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * @Description: 异步语音识别
 * @Author: jay.he
 * @Date: 2025-08-18 19:26
 * @Version: 1.0
 **/
@Slf4j
@Service
public class AsyncRecognitionServiceImpl implements AsyncRecognitionService {

    @Value("${funasr.server.url}")
    private String funasrServerUrl;

    @Value("#{'${funasr.server.urls}'.split(',')}")
    private List<String> funasrServerUrls;

    @Value("${upload.file.temp.path}")
    private String uploadTempPath;

    @Value("${sof-env}")
    private String env;

    @Resource
    private S3ClientApi s3ClientApi;

    @Resource
    private AsrRecognitionRecordMapper asrRecognitionRecordMapper;

    @Resource
    private ThreadPoolTaskExecutor recognitionExecutorService;

    @Resource
    private ExecutorService uploadS3ExecutorService;


    @Override
    public String unzipAndAsyncRecognize(String taskId, MultipartFile file) throws IOException {

        // 1.保存原始文件
        String fileName = file.getOriginalFilename();
        String savePath = uploadTempPath + fileName;
        Path path = Paths.get(uploadTempPath);
        if (!Files.exists(path)) {
            Files.createDirectories(path);
        }
        file.transferTo(new File(savePath));
        log.info("[AsyncRecognitionService] saveOriginalFile, path = {}", savePath);

        // 2.原文件上传S3
        long start = System.currentTimeMillis();
        String s3Url = uploadToS3(taskId, savePath);
        long end = System.currentTimeMillis();
        log.info("上传文件耗时：{} ms", (end - start));
        log.info("[AsyncRecognitionService] upload to S3，s3-url:{}", s3Url);

        // 3.解压原文件并获取音视频文件元数据列表
        UploadFileInfoVO uploadFileInfoVO = FileUtils.extractAndListMediaFiles(savePath);
        List<FileMetaInfo> fileMetaDataList = uploadFileInfoVO.getFileMetaInfoList();

        handleMetaList(fileMetaDataList, savePath, taskId, uploadFileInfoVO.isNeedDel());

        log.info("[AsyncRecognitionService] extractAndListMediaFiles, fileMetadataList = {}",
                JacksonUtils.toJSONString(fileMetaDataList));

        // 4.异步识别
        log.info("[AsyncRecognitionService] start async recognition ");
        recognize(taskId, fileMetaDataList);

        return s3Url;
    }

    /**
     * 处理文件元数据列表
     *
     * @param fileMetaDataList
     */
    private void handleMetaList(List<FileMetaInfo> fileMetaDataList, String savePath, String taskId, boolean needDel) {
        //1.按元数据创建时间排序
        fileMetaDataList.sort(Comparator.comparing(FileMetaInfo::getCreateTime));
        //2.为每个文件MetaInfo添加序号(时间顺序 --等价--> 即当前元素索引)
        for (int i = 0; i < fileMetaDataList.size(); i++) {
            fileMetaDataList.get(i).setSeqNo(i);
        }

        if (needDel) {
            //3.删除本地原文件
            delTempFile(savePath);
        }

        //4.清理相同taskId下的其它识别结果
        asrRecognitionRecordMapper.invalidRecords(taskId, env);
    }

    /**
     * 本地文件上传S3
     *
     * @param savePath
     * @return
     */
    private String uploadToS3(String taskId, String savePath) {
        File file = new File(savePath);
        if (!file.exists()) {
            log.error("File not found: {}", savePath);
            throw new RuntimeException("File not found: " + savePath);
        }

        // 定义分段上传阈值（例如10MB）
        long multipartThreshold = 10 * 1024 * 1024;

        try {
            String fileName = taskId + "/" + file.getName();
            String s3Url;

            // 如果文件大于阈值，使用分段上传
            if (file.length() > multipartThreshold) {
                log.info("File size {} bytes exceeds threshold {}, using multipart upload",
                        file.length(), multipartThreshold);
                s3ClientApi.multipartUploadAsync(fileName, file, uploadS3ExecutorService);
            } else {
                // 否则使用普通上传
                try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file))) {
                    s3ClientApi.putObjByStream(fileName, inputStream);
                }
            }

            // 获取上传的S3文件url
            s3Url = s3ClientApi.getPrivateURL(fileName);
            if (s3Url == null || s3Url.isEmpty()) {
                throw new RuntimeException("S3 upload returned empty URL");
            }

            log.info("Successfully uploaded file to S3: {}", s3Url);
            return s3Url;

        } catch (Exception e) {
            log.error("Failed to upload file to S3: {}", savePath, e);
            return null;
        }
    }


    private void delTempFile(String filePath) {
        FileUtils.delFile(filePath);
    }

    @Override
    public void recognize(String taskId, List<FileMetaInfo> fileMetadataList) {
        if (CollectionUtils.isEmpty(fileMetadataList)) {
            return; // 如果列表为空，直接返回
        }
        for (FileMetaInfo fileMetaInfo : fileMetadataList) {
            CompletableFuture.runAsync(() -> {
                recognize(taskId, fileMetaInfo);
            }, recognitionExecutorService);
        }
    }

    @Override
    public void recognize(String taskId, FileMetaInfo fileMetaInfo) {
        String filePath = fileMetaInfo.getFilePath();
        CountDownLatch latch = new CountDownLatch(1); // 创建计数器
        WebSocketClient client = null;
        String asrServerUrl = funasrServerUrl;
        try {
            asrServerUrl = Optional.ofNullable(funasrServerUrls)
                    .map(list -> list.get(new Random().nextInt(list.size())))
                    .orElse(funasrServerUrl);
            client = new WebSocketClient(new URI(asrServerUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    log.info("Connected to FunASR");
                }

                @Override
                public void onMessage(String message) {
                    // FunASR 返回结果 JSON
                    parseResult(taskId, message, fileMetaInfo);
                    latch.countDown(); // 收到消息后计数器减1
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    log.info("Closed: reason:{}, time:{}", reason, new Date().toLocaleString());
                    latch.countDown(); // 连接关闭时也释放等待
                    //删除临时文件
                    delTempFile(filePath);
                }

                @Override
                public void onError(Exception ex) {
                    log.info("任务执行fail，taskId={} ", taskId, ex);
                    latch.countDown(); // 出错时也释放等待
                    //删除临时文件
                    delTempFile(filePath);
                }
            };

            client.connectBlocking();

            JSONObject initConfigJson = new JSONObject();
            initConfigJson.put("mode", "offline");
            initConfigJson.put("wav_name", new File(filePath).getName());
            initConfigJson.put("wav_format", "wav");
            initConfigJson.put("itn", true);
            //发送控制 JSON
            client.send(initConfigJson.toString());

            // 发送音频数据
            byte[] audioBytes = Files.readAllBytes(Paths.get(filePath));
            client.send(audioBytes);

            // 发送结束标志
            JSONObject endConfigJson = new JSONObject();
            endConfigJson.put("is_speaking", false);

            client.send(endConfigJson.toString());


            //使用CountDownLatch，返回结果后，就关闭WebSocket连接
            // 等待收到消息或连接关闭
            latch.await(); // 阻塞等待，直到计数器归零

        } catch (Exception e) {
            log.error("任务执行失败，taskId={}, asrServerUrl={}, error:", taskId, asrServerUrl, e);
        } finally {
            if (client != null && client.isOpen()) {
                client.close();
            }
        }
    }


    private String parseResult(String taskId, String message, FileMetaInfo fileMetaInfo) {
        log.info("语音识别结果：result:{}", message);
        if (StringUtils.isNotEmpty(message)) {
            AsrRecognitionResult asrResult = JacksonUtils.fromJSONString(message, AsrRecognitionResult.class);
            //原文件上传S3
            String s3Url = uploadToS3(taskId, fileMetaInfo.getFilePath());
            log.info("[AsyncRecognitionService] upload to S3，s3-url:{}", s3Url);
            AsrRecognitionRecordDO record = new AsrRecognitionRecordDO();
            Date now = new Date();
            record.setOrderNo(taskId);
            record.setMetaInfo(JacksonUtils.toJSONString(fileMetaInfo));
            record.setResult(Optional.ofNullable(asrResult)
                    .map(AsrRecognitionResult::getText)
                    .orElse(Strings.EMPTY));
            record.setStatus(0);
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setEnv(env);
            record.setSeqNo(fileMetaInfo.getSeqNo());
            record.setResourceUrl(s3Url);
            //TODO: 存DB
            log.info("save result to record = {}", JacksonUtils.toJSONString(record));
            asrRecognitionRecordMapper.insert(record);
        }
        return message;
    }
}
