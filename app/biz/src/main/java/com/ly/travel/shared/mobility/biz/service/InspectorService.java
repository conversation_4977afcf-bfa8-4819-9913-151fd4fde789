package com.ly.travel.shared.mobility.biz.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.net.URLDecoder;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.sof.utils.page.PageList;
import com.ly.sof.utils.page.PageUtils;
import com.ly.travel.shared.mobility.biz.model.dto.inspector.InspectorInfoCacheDTO;
import com.ly.travel.shared.mobility.biz.model.dto.order.OrderInfoDTO;
import com.ly.travel.shared.mobility.biz.model.req.asrorder.AsrOrderInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.asrorder.UploadSoundInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorBaseReq;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorImproveInfoReq;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorLoginReq;
import com.ly.travel.shared.mobility.biz.model.req.order.OrderQueryReq;
import com.ly.travel.shared.mobility.biz.model.resp.AsrOrderInfoResp;
import com.ly.travel.shared.mobility.biz.model.resp.OrderTypeOptionResp;
import com.ly.travel.shared.mobility.biz.model.resp.ServiceTagResp;
import com.ly.travel.shared.mobility.biz.model.resp.inspector.InspectorJumpPageResp;
import com.ly.travel.shared.mobility.biz.model.resp.inspector.InspectorLoginResp;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrAccountDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao.AsrOrderInfoDao;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrAccountDO;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrOrderInfoDO;
import com.ly.travel.shared.mobility.integration.constant.CommonConstant;
import com.ly.travel.shared.mobility.integration.enums.*;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreException;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreWarnException;
import com.ly.travel.shared.mobility.integration.utils.AsrCoreConfigCenterUtils;
import com.ly.travel.shared.mobility.integration.utils.CacheKeyUtils;
import com.ly.travel.shared.mobility.integration.utils.DeepCopyUtils;
import com.ly.travel.shared.mobility.integration.utils.dto.DriverServiceTagDTO;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import redis.clients.jedis.JedisCluster;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InspectorService {
    @Resource
    private JedisCluster jedisCluster;
    @Resource
    private AsrOrderInfoDao asrOrderInfoDao;
    @Resource
    private OrderService orderService;
    @Resource
    private AsyncRecognitionService asyncRecognitionService;
    @Resource
    private AccountService accountService;
    @Resource
    private AsrAccountDao accountDao;

    public InspectorLoginResp login(InspectorLoginReq req) {
        //用户登录
        AsrAccountDO account = accountService.getAccount(req.getAccount());
        if (account == null || AccountStatusEnum.INVALID.getStatus().equals(account.getStatus())) {
            throw new AsrCoreWarnException("用户信息不存在");
        }

        if (!account.getPassword().equals(req.getPassword())) {
            throw new AsrCoreWarnException("密码错误");
        }
        //生成token
        InspectorInfoCacheDTO cacheDTO = toInspectorCacheDTO(account);
        //存入缓存
        setInspectorCache(cacheDTO);
        InspectorLoginResp resp = new InspectorLoginResp();

        Integer jumpPage = InspectorPageEnum.ORDER_LIST_PAGE.getCode();
        if (returnImprovePage(account)) {
            jumpPage = InspectorPageEnum.IMPROVE_PAGE.getCode();
        }
        resp.setJumpPage(jumpPage);
        resp.setToken(cacheDTO.getToken());
        resp.setAccount(cacheDTO.getAccount());
        //写入cookie
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = attributes.getResponse();
        Cookie cookie = new Cookie(CommonConstant.LOGIN_COOKIE_KEY, URLEncoder.encode(JSON.toJSONString(resp)));
        //cookie 一天过期
        cookie.setMaxAge(24 * 3600);
        response.addCookie(cookie);
        return resp;
    }


    public void improveInfo(InspectorImproveInfoReq req) {

        List<AsrAccountDO> accountList = accountService.getAccountList();
        AsrAccountDO inspectorInfo = accountList.stream().filter(s -> s.getMobile().equals(req.getAccount())).findFirst().orElse(null);
        if (inspectorInfo == null) {
            return;
        }
        if (!AccountStatusEnum.WAIT_COMPLETE.getStatus().equals(inspectorInfo.getStatus())) {
            throw new AsrCoreWarnException("信息已完善,无需再次提交");
        }

        inspectorInfo.setMailAddress(req.getPostAddress());
        inspectorInfo.setMailPhone(req.getMailPhone());
        inspectorInfo.setMailName(req.getMailName());
        inspectorInfo.setUpdateBy(req.getAccount());
        inspectorInfo.setUpdateTime(new Date());
        inspectorInfo.setStatus(AccountStatusEnum.WAIT_SHIP.getStatus());
        boolean success = accountDao.getBaseMapper().updateById(inspectorInfo) > 0;
        if (!success) {
            throw new AsrCoreWarnException("完善信息失败");
        }

    }

    /**
     * 设置Inspector缓存
     *
     * @param cacheDTO 需要存入缓存的InspectorCacheDTO对象
     */
    private void setInspectorCache(InspectorInfoCacheDTO cacheDTO) {
        String cacheKey = CacheKeyUtils.getInspectorCacheKey(cacheDTO.getAccount());
        //存入缓存
        jedisCluster.setex(cacheKey, 24 * 3600, JSON.toJSONString(cacheDTO));
    }

    private InspectorInfoCacheDTO toInspectorCacheDTO(AsrAccountDO account) {
        InspectorInfoCacheDTO cacheDTO = new InspectorInfoCacheDTO();
        cacheDTO.setAccount(account.getMobile());
        cacheDTO.setToken(UUID.randomUUID().toString());
        return cacheDTO;
    }

    public void checkInspector(InspectorBaseReq baseReq) {
        InspectorLoginResp loginResp = getLoginCookie();
        if (loginResp != null) {
            baseReq.setToken(loginResp.getToken());
            baseReq.setAccount(loginResp.getAccount());
        }

        String account = baseReq.getAccount();
        //获取缓存信息
        InspectorInfoCacheDTO inspectorCacheDTO = queryInspectorCacheDTO(account);
        //缓存过期,跳转登录页
        if (inspectorCacheDTO == null) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "token过期,请重新登录");
        }
        AsrAccountDO userInfo = accountService.getAccount(inspectorCacheDTO.getAccount());
        if (userInfo == null || AccountStatusEnum.INVALID.getStatus().equals(userInfo.getStatus())) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "未获取到检查员信息");
        }
        if (!inspectorCacheDTO.getToken().equals(baseReq.getToken())) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "token不一致,请重新登录");
        }
        baseReq.setInspectorInfo(accountToDTO(userInfo));
    }

    private InspectorInfoCacheDTO queryInspectorCacheDTO(String account) {
        String cacheKey = CacheKeyUtils.getInspectorCacheKey(account);
        String inspectorInfoStr = jedisCluster.get(cacheKey);

        //缓存过期,跳转登录页
        if (StringUtils.isBlank(inspectorInfoStr)) {
            return null;
        }
        InspectorInfoCacheDTO userInfoCacheDTO = JSON.parseObject(inspectorInfoStr, InspectorInfoCacheDTO.class);
        return userInfoCacheDTO;
    }

    private InspectorLoginResp getLoginCookie() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        Cookie[] cookies = request.getCookies();

        if (ArrayUtils.isEmpty(cookies)) {
            return null;
        }
        Cookie loginTokenCookie = Arrays.asList(request.getCookies()).stream().filter(s -> s.getName().equals(CommonConstant.LOGIN_COOKIE_KEY)).findFirst().orElse(null);
        if (loginTokenCookie == null) {
            return null;
        }
        String loginTokenStr = URLDecoder.decode(loginTokenCookie.getValue(), Charset.forName("UTF-8"));
        InspectorLoginResp loginResp = JSON.parseObject(loginTokenStr, InspectorLoginResp.class);
        return loginResp;
    }


    public InspectorJumpPageResp queryJumpPage() {
        String account = StringUtils.EMPTY;
        String token = StringUtils.EMPTY;

        InspectorLoginResp loginResp = getLoginCookie();
        if (loginResp != null) {
            account = loginResp.getAccount();
            token = loginResp.getToken();
        }

        //获取缓存信息
        InspectorInfoCacheDTO inspectorCacheDTO = queryInspectorCacheDTO(account);
        //缓存过期,跳转登录页
        if (inspectorCacheDTO == null) {
            log.info("account:{},缓存为空,返回登录页", account);
            return new InspectorJumpPageResp(InspectorPageEnum.LOGIN_PAGE.getCode());
        }
        if (!inspectorCacheDTO.getToken().equals(token)) {
            log.info("account:{},缓存token:{},请求token:{},token不一致,返回登录页", account, inspectorCacheDTO.getToken(), token);
            return new InspectorJumpPageResp(InspectorPageEnum.LOGIN_PAGE.getCode());
        }
        //统一配置中再获取下账号信息,防止统一配置中删除了
        AsrAccountDO userInfo = accountService.getAccount(inspectorCacheDTO.getAccount());
        if (userInfo == null || AccountStatusEnum.INVALID.getStatus().equals(userInfo.getStatus())) {
            log.info("account:{},统一配置已删除或无效了该检查员信息,返回登录页", account);
            return new InspectorJumpPageResp(InspectorPageEnum.LOGIN_PAGE.getCode());
        }
        //缓存续期
        setInspectorCache(inspectorCacheDTO);
        if (returnImprovePage(userInfo)) {
            log.info("account:{},邮寄信息或收款信息为空,返回完善信息页", account);
            return new InspectorJumpPageResp(InspectorPageEnum.IMPROVE_PAGE.getCode());
        }
        log.info("account:{},返回订单列表页", account);
        return new InspectorJumpPageResp(InspectorPageEnum.ORDER_LIST_PAGE.getCode());
    }

    private boolean returnImprovePage(AsrAccountDO account) {
        return AccountStatusEnum.WAIT_COMPLETE.getStatus().equals(account.getStatus());
    }

    /**
     * 获取当前用户信息
     *
     * @return
     */
    public InspectorAccountDTO getUserInfo(boolean showPassword) {
        String account = StringUtils.EMPTY;
        String token = StringUtils.EMPTY;

        InspectorLoginResp loginResp = getLoginCookie();
        if (loginResp != null) {
            account = loginResp.getAccount();
            token = loginResp.getToken();
        }

        //获取缓存信息
        InspectorInfoCacheDTO inspectorCacheDTO = queryInspectorCacheDTO(account);
        //缓存过期,抛出异常
        if (inspectorCacheDTO == null) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "token过期,请重新登录");
        }
        if (!inspectorCacheDTO.getToken().equals(token)) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "token不一致,请重新登录");
        }
        AsrAccountDO userInfo = accountService.getAccount(inspectorCacheDTO.getAccount());
        if (userInfo == null || AccountStatusEnum.INVALID.getStatus().equals(userInfo.getStatus())) {
            throw new AsrCoreException(AsrCoreErrorEnum.TOKEN_FAIL.getCode(), "未获取到检查员信息");
        }
        //缓存续期
        setInspectorCache(inspectorCacheDTO);
        InspectorAccountDTO dto = accountToDTO(userInfo);
        // 隐藏密码信息
        if (!showPassword){
            userInfo.setPassword(null);
        }
        return dto;

    }

    public InspectorAccountDTO accountToDTO(AsrAccountDO accountDO){
        InspectorAccountDTO dto = new InspectorAccountDTO();
        dto.setUserAccount(accountDO.getMobile());
        dto.setName(accountDO.getName());
        dto.setPassword(accountDO.getPassword());
        dto.setMobileNo(accountDO.getMobile());
        dto.setMailAddress(accountDO.getMailAddress());
        dto.setStatus(accountDO.getStatus());
        dto.setUpdateBy(accountDO.getUpdateBy());
        dto.setUpdateTime(DateUtil.formatDateTime(accountDO.getUpdateTime()));
        dto.setPaymentMethod(accountDO.getPaymentMethod());
        dto.setPaymentCardNo(accountDO.getPaymentCardNo());
        dto.setPaymentName(accountDO.getPaymentName());
        dto.setMailName(accountDO.getMailName());
        dto.setMailPhone(accountDO.getMailPhone());
        return dto;
    }

    private void syncSfcOrderToAsrOrder(AsrOrderInfoReq req) {
        String syncCacheKey = CacheKeyUtils.getAsrOrderSyncCacheKey(req.getAccount());
        String syncVal = jedisCluster.get(syncCacheKey);
        if (StringUtils.isNotBlank(syncVal)) {
            log.warn("当前正在同步数据,忽略此次操作");
            return;
        }

        try {
            jedisCluster.setex(syncCacheKey, 3600, "1");
            String mobileNo = req.getInspectorInfo().getMobileNo();
            OrderQueryReq orderQueryReq = new OrderQueryReq();
            orderQueryReq.setPhoneNo(mobileNo);
            //查询接口数据
            Date startTime = DateUtil.offsetDay(DateUtil.beginOfDay(new Date()), -AsrCoreConfigCenterUtils.getSfcOrderBeforeDays());
            orderQueryReq.setStartTime(DateUtil.formatDateTime(startTime));
            orderQueryReq.setEndTime(DateUtil.formatDateTime(DateUtil.offsetDay(DateUtil.beginOfDay(new Date()),1)));
            List<OrderInfoDTO> orderList = orderService.querySfcOrderList(orderQueryReq);
//            List<OrderInfoDTO> orderList = orderService.queryFinishOrderList(orderQueryReq);
            Map<String, AsrOrderInfoDO> asrOrderMap = asrOrderInfoDao.queryExists(mobileNo).stream().collect(Collectors.toMap(s -> s.getOrderSerialNo(), p -> p, (v1, v2) -> v1));

            List<AsrOrderInfoDO> addedAsrOrderInfoList = new ArrayList<>();
            for (OrderInfoDTO orderInfoDTO : orderList) {
                AsrOrderInfoDO orderInfoDO = asrOrderMap.get(orderInfoDTO.getOrderNo());
                if (orderInfoDO != null) {
                    continue;
                }
                orderInfoDO = new AsrOrderInfoDO();

                orderInfoDO.setOrderSerialNo(orderInfoDTO.getOrderNo());
                orderInfoDO.setMobileNo(mobileNo);
                if (StringUtils.isNotBlank(orderInfoDTO.getPassengerOnCarTime())) {
                    orderInfoDO.setTripStartTime(DateUtil.parseDateTime(orderInfoDTO.getPassengerOnCarTime()));
                }
                if (StringUtils.isNotBlank(orderInfoDTO.getPassengerArriveTime())) {
                    orderInfoDO.setTripEndTime(DateUtil.parseDateTime(orderInfoDTO.getPassengerArriveTime()));
                }
                orderInfoDO.setDriverName(orderInfoDTO.getDriverName());
                orderInfoDO.setPlateNo(orderInfoDTO.getCarNo());
                orderInfoDO.setUploadStatus(AsrOrderUploadStatusEnum.WAIT_UPLOAD.getStatus());
                orderInfoDO.setAuditStatus(AsrOrderAuditStatusEnum.WAIT_AUDIT.getStatus());
                addedAsrOrderInfoList.add(orderInfoDO);
            }
            //批量新增
            if (CollectionUtils.isNotEmpty(addedAsrOrderInfoList)) {
                asrOrderInfoDao.saveBatch(addedAsrOrderInfoList);
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            jedisCluster.del(syncCacheKey);
        }
    }


    public PageList<AsrOrderInfoResp> queryAsrOrderInfoPage(AsrOrderInfoReq req) {
        //查询下交易订单,然后把订单数据同步下
        syncSfcOrderToAsrOrder(req);
        //分页查询列表
        String mobileNo = req.getInspectorInfo().getMobileNo();
        Page<AsrOrderInfoDO> page = asrOrderInfoDao.queryPage(req, mobileNo, req.getOrderSerialNo(), req.getPlateNo(),
                req.getUploadStatus(), req.getAuditStatus(), req.getTripStartTime(), req.getTripEndTime(), req.getOrderType());
        List<AsrOrderInfoResp> respList = new ArrayList<>();
        for (
                AsrOrderInfoDO infoDO : page.getRecords()) {
            AsrOrderInfoResp resp = DeepCopyUtils.map(infoDO, AsrOrderInfoResp.class);
            resp.setUploadStatusDesc(AsrOrderUploadStatusEnum.getDescByCode(resp.getUploadStatus()));
            resp.setAuditStatusDesc(AsrOrderAuditStatusEnum.getDescByCode(resp.getAuditStatus()));
            respList.add(resp);
        }

        PageList<AsrOrderInfoResp> pageList = PageUtils.createPageList(respList, req, (int) page.getTotal());
        return pageList;
    }

    public void uploadSoundInfo(CommonsMultipartFile file, UploadSoundInfoReq req) {
        //校验订单是否属于本人
        AsrOrderInfoDO orderInfoDO = asrOrderInfoDao.queryByOrderSerialNo(req.getOrderSerialNo());
        if (orderInfoDO == null) {
            throw new AsrCoreWarnException("未获取到订单信息");
        }
        if (!orderInfoDO.getMobileNo().equals(req.getInspectorInfo().getMobileNo())) {
            throw new AsrCoreWarnException("非本人订单");
        }
        if (orderInfoDO.getAuditStatus().equals(AsrOrderAuditStatusEnum.HAD_AUDIT.getStatus())) {
            throw new AsrCoreWarnException("录音已审核通过,无需再次上传");
        }

        String fileUrl = null;
        try {
            fileUrl = asyncRecognitionService.unzipAndAsyncRecognize(req.getOrderSerialNo(), file);
        } catch (IOException e) {
            throw new AsrCoreWarnException(e.getMessage());
        }

        //保存订单信息
        orderInfoDO.setFileUrl(fileUrl);
        orderInfoDO.setUploadTime(new Date());
        orderInfoDO.setUploadStatus(AsrOrderUploadStatusEnum.HAD_UPLOAD.getStatus());
        orderInfoDO.setServiceTag(req.getServiceTag());
        orderInfoDO.setServiceReview(req.getServiceReview());
        orderInfoDO.setRatingScore(req.getRatingScore());
        asrOrderInfoDao.updateById(orderInfoDO);
    }

    /**
     * 获取订单类型选项
     *
     * @return
     */
    public List<OrderTypeOptionResp> getOrderTypeOptions() {
        return OrderTypeOptionResp.getOrderTypeOptions();
    }

    /**
     * 根据订单类型获取服务标签
     *
     * @param orderType
     * @return
     */
    public List<ServiceTagResp> getServiceTags(Integer orderType) {
        DriverServiceTagDTO driverServiceTagList = AsrCoreConfigCenterUtils.getDriverServiceTagList();
        return driverServiceTagList.getServiceTagList()
                .stream()
                .filter(Objects::nonNull)
                .filter(item -> Optional.ofNullable(item.getSupportOrderTypes())
                        .orElse(Collections.EMPTY_LIST)
                        .contains(String.valueOf(orderType))
                )
                .map(tagInfo -> ServiceTagResp.builder()
                        .label(tagInfo.getTagName())
                        .value(tagInfo.getTagCode())
                        .build())
                .collect(Collectors.toList());

//        List<ServiceTagResp> tags = new ArrayList<>();
//
//        // 通用服务标签
//        tags.add(new ServiceTagResp("礼貌用语", "礼貌用语"));
//        tags.add(new ServiceTagResp("提前联系", "提前联系"));
//        tags.add(new ServiceTagResp("提供瓶装水", "提供瓶装水"));
//        tags.add(new ServiceTagResp("不闯聊", "不闯聊"));
//
//        // 根据订单类型添加特定标签
//        if (orderType != null) {
//            if (orderType == 12 || orderType == 13) { // 接机/送机
//                tags.add(new ServiceTagResp("开关车门", "开关车门"));
//                tags.add(new ServiceTagResp("提拿行李", "提拿行李"));
//                tags.add(new ServiceTagResp("着装统一", "着装统一"));
//            } else if (orderType == 14 || orderType == 15) { // 接站/送站
//                tags.add(new ServiceTagResp("开关车门", "开关车门"));
//                tags.add(new ServiceTagResp("提拿行李", "提拿行李"));
//            } else if (orderType == 80) { // 顺风车
//                tags.add(new ServiceTagResp("接送进小区", "接送进小区"));
//            }
//        }
//
//        return tags;
    }
}
