package com.ly.travel.shared.mobility.biz.model.req.asrorder;

import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorBaseReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
public class UploadSoundInfoReq extends InspectorBaseReq {
    /**
     * 订单流水号
     */
    @NotBlank(message = "订单流水号不能为空")
    private String orderSerialNo;

    /**
     * 司机服务标签
     */
    private String serviceTag;

    /**
     * 服务点评
     */
    private String serviceReview;

    /**
     * 司机服务评分
     */
    private BigDecimal driverScore;
}
