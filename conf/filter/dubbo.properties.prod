#basic
sof.version=${sof_core_version}
app.name=shared-mobility-asr-core
app.version=*******
app.type=${project_type}
app.root=${user.home}/${app.name}
sof-env=prod

#db
uniform.env=product
uniform.skyCode=groundtravel.shared.mobility.asr.core
uniform.dbName=TCTravelCarAsr

#dubbo
dubbo.application.name=shared-mobility-asr-core
dubbo.registry.address=dsf2.17usoft.com
dubbo.container=spring,log4js
dubbo.service.deploy.container=tomcat
dubbo.port=${PORT1}
dubbo.tcdsfGroup.gsName=dsf.group.name
dubbo.tcdsfGroup.version=dsf.group.version

#turbomq\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709mq\uFF0C\u53EF\u4EE5\u5220\u9664
mq.nameSrvAddress=mqnameserver.17usoft.com:9876;mqnameserverbak.17usoft.com:9876

#drm\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709drm\uFF0C\u53EF\u4EE5\u5220\u9664
conf.domain=shared-mobility-asr-core
zkconnect=public3.zk.17usoft.com:2181
rootPath=flight

#redis\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709redis\uFF0C\u53EF\u4EE5\u5220\u9664
redis.groupName=redisGroupName

#kafka\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709kafka\uFF0C\u53EF\u4EE5\u5220\u9664
kfk.projectName=kfkProjectName
kfk.projectCode=kfkProjectCode

#\u77ED\u4FE1\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709\u77ED\u4FE1\u53D1\u9001\uFF0C\u53EF\u4EE5\u5220\u9664
mq.goto.message.topic=flight_gotocore_tp_message
mq.goto.message.code=flight_e_gotocore_message_template_send

#ssoe
sec.interface.url=http://authority.17usoft.com/Interface/Service.ashx
sec.403.url=/
sec.index.url=/index
sec.interface.projectCode=yourProjectCode
sec.sso.url=http://flightadminapi.17usoft.com/sso
sec.currentSys.url=http://flightadminapi.17usoft.com/shared-mobility-asr-core
session.timeout=3600

#http client
http_read_timeout=5000
connect_timeout=5000

car.mng.url=http://tcwireless.17usoft.com/carmng/

dsf.car.shared.mobility.order.service.gsName=dsf.car.order.service
dsf.car.shared.mobility.order.service.version=0.1.2.4

dsf.car.shared.mobility.order.core.gsName=dsf.car.order.core
dsf.car.shared.mobility.order.core.version=latest

labrador.shared.car.order.url=http://servicegw.ly.com/gateway/sharedcar/prod
labrador.shared.car.order.token=748463d5-8462-47d4-8726-ac27aad952ab

approve.apply.detail.url=http://tcwireless.17usoft.com/supply/index/
marketing.mng2.url=http://marketing.travel.17usoft.com/marketingmng2

#crm-client
travel.mobility.supply.crm.core.dsf.gsName=dsf.car.shared.mobility.supply.crm.core

#语音识别服务地址
funasr.server.url=ws://172.26.209.100:10096
funasr.server.urls=ws://172.26.209.100:11091,ws://172.26.209.100:11092,ws://172.26.209.100:11093,ws://172.26.209.100:10096
#音视频临时文件保存路径
upload.file.temp.path=/data/temp/asr_files/

# car.order.service
car.order.service.gsName=dsf.car.order.service
car.order.service.version=0.1.2.4