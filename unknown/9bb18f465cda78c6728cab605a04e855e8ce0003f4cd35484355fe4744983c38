<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">质量抽检系统</h2>
      <el-form :model="formData" ref="form" label-width="20">
        <el-form-item label="账户" prop="account" :rules="[{ required: true, message: '账户不能为空' }]">
          <el-input v-model="formData.account" placeholder="请输入账户" maxlength="30"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" :rules="[{ required: true, message: '密码不能为空' }]">
          <el-input type="password" v-model="formData.password" maxlength="30" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loginClick" :loading="loadingLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>


<script>

let orgFormData = {
  account: '',
  password: '',
}


export default {
  components: {},
  data() {
    return {
      formData: this.deepCopy(orgFormData),
      loadingLogin: false
    }
  },

  created() {
  },
  mounted() {

  },
  filters: {},
  methods: {
    loginClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loadingLogin = true
          let param = {
            url: 'inspector/login',
            method: 'POST',
            data: this.formData
          }
          this.$http(param).then((res) => {
            if (res.success) {
              this.$message.success('登录成功')
              let loginTokenInfo = res.data
              if (res.data.jumpPage === 2) {
                this.$router.push('/improveInfo')
              } else {
                this.$router.push('/inspector/orders')
              }

            } else {
              this.$message.error(res.msg)
            }
            this.loadingLogin = false

          }).catch(err => {
            console.log(err);
            this.$message.error('登录失败')
            this.loadingLogin = false
          })
        }
      });

    },
  }
};
</script>

<style>
</style>
